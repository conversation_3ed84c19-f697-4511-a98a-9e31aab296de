<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapa de Identidad - <PERSON></title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .identity-map {
            position: relative;
            width: 800px;
            height: 600px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .center-circle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 120px;
            background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
            z-index: 10;
        }

        .branch {
            position: absolute;
            background: #333;
            transform-origin: 0 50%;
        }

        .category {
            position: absolute;
            padding: 8px 12px;
            border-radius: 15px;
            color: white;
            font-size: 11px;
            font-weight: 600;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transform: translate(-50%, -50%);
            min-width: 80px;
        }

        .valores { background: linear-gradient(45deg, #ff6b6b, #ee5a24); }
        .profesion { background: linear-gradient(45deg, #4834d4, #686de0); }
        .educacion { background: linear-gradient(45deg, #00d2d3, #54a0ff); }
        .tecnologias { background: linear-gradient(45deg, #5f27cd, #341f97); }
        .personalidad { background: linear-gradient(45deg, #ff9ff3, #f368e0); }
        .origen { background: linear-gradient(45deg, #feca57, #ff9f43); }
        .idiomas { background: linear-gradient(45deg, #48dbfb, #0abde3); }
        .metas { background: linear-gradient(45deg, #1dd1a1, #10ac84); }

        .branch-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, #4facfe, transparent);
            transform-origin: 0 50%;
        }

        .title {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 24px;
            font-weight: bold;
            color: #333;
            text-align: center;
        }

        .subtitle {
            position: absolute;
            top: 55px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 14px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="identity-map">
        <div class="title">Mapa de Identidad</div>
        <div class="subtitle">¿Quién soy yo?</div>
        
        <div class="center-circle">
            <div>
                <strong>JUAN NICOLAS</strong><br>
                <strong>PARDO TORRES</strong>
            </div>
        </div>

        <!-- Valores -->
        <div class="branch-line" style="top: 300px; left: 400px; width: 120px; transform: rotate(-45deg);"></div>
        <div class="category valores" style="top: 220px; left: 580px;">
            <div>Autodidacta</div>
        </div>
        
        <div class="branch-line" style="top: 300px; left: 400px; width: 100px; transform: rotate(-30deg);"></div>
        <div class="category valores" style="top: 240px; left: 550px;">
            <div>Aprendizaje Continuo</div>
        </div>

        <div class="branch-line" style="top: 300px; left: 400px; width: 110px; transform: rotate(-60deg);"></div>
        <div class="category valores" style="top: 200px; left: 520px;">
            <div>Calidad del Software</div>
        </div>

        <!-- Profesión -->
        <div class="branch-line" style="top: 300px; left: 400px; width: 130px; transform: rotate(45deg);"></div>
        <div class="category profesion" style="top: 380px; left: 580px;">
            <div>Desarrollador Full Stack</div>
        </div>

        <div class="branch-line" style="top: 300px; left: 400px; width: 110px; transform: rotate(60deg);"></div>
        <div class="category profesion" style="top: 400px; left: 520px;">
            <div>Ingeniero de Sistemas</div>
        </div>

        <div class="branch-line" style="top: 300px; left: 400px; width: 100px; transform: rotate(30deg);"></div>
        <div class="category profesion" style="top: 360px; left: 550px;">
            <div>Desarrollador Junior</div>
        </div>

        <!-- Educación -->
        <div class="branch-line" style="top: 300px; left: 400px; width: 120px; transform: rotate(135deg);"></div>
        <div class="category educacion" style="top: 380px; left: 220px;">
            <div>Universidad El Bosque</div>
        </div>

        <div class="branch-line" style="top: 300px; left: 400px; width: 110px; transform: rotate(150deg);"></div>
        <div class="category educacion" style="top: 400px; left: 280px;">
            <div>Harvard CS50</div>
        </div>

        <div class="branch-line" style="top: 300px; left: 400px; width: 100px; transform: rotate(120deg);"></div>
        <div class="category educacion" style="top: 360px; left: 250px;">
            <div>Full Stack Open</div>
        </div>

        <!-- Tecnologías -->
        <div class="branch-line" style="top: 300px; left: 400px; width: 120px; transform: rotate(-135deg);"></div>
        <div class="category tecnologias" style="top: 220px; left: 220px;">
            <div>Next.js & React</div>
        </div>

        <div class="branch-line" style="top: 300px; left: 400px; width: 110px; transform: rotate(-150deg);"></div>
        <div class="category tecnologias" style="top: 200px; left: 280px;">
            <div>Spring Boot</div>
        </div>

        <div class="branch-line" style="top: 300px; left: 400px; width: 100px; transform: rotate(-120deg);"></div>
        <div class="category tecnologias" style="top: 240px; left: 250px;">
            <div>PostgreSQL</div>
        </div>

        <!-- Personalidad -->
        <div class="branch-line" style="top: 300px; left: 400px; width: 90px; transform: rotate(0deg);"></div>
        <div class="category personalidad" style="top: 300px; left: 540px;">
            <div>Comprometido</div>
        </div>

        <div class="branch-line" style="top: 300px; left: 400px; width: 85px; transform: rotate(15deg);"></div>
        <div class="category personalidad" style="top: 320px; left: 530px;">
            <div>Adaptable</div>
        </div>

        <!-- Origen -->
        <div class="branch-line" style="top: 300px; left: 400px; width: 90px; transform: rotate(180deg);"></div>
        <div class="category origen" style="top: 300px; left: 260px;">
            <div>Bogotá, Colombia</div>
        </div>

        <div class="branch-line" style="top: 300px; left: 400px; width: 85px; transform: rotate(165deg);"></div>
        <div class="category origen" style="top: 320px; left: 270px;">
            <div>Colombiano</div>
        </div>

        <!-- Idiomas -->
        <div class="branch-line" style="top: 300px; left: 400px; width: 80px; transform: rotate(90deg);"></div>
        <div class="category idiomas" style="top: 420px; left: 400px;">
            <div>Español Nativo</div>
        </div>

        <div class="branch-line" style="top: 300px; left: 400px; width: 75px; transform: rotate(105deg);"></div>
        <div class="category idiomas" style="top: 430px; left: 370px;">
            <div>Inglés Avanzado</div>
        </div>

        <!-- Metas -->
        <div class="branch-line" style="top: 300px; left: 400px; width: 80px; transform: rotate(-90deg);"></div>
        <div class="category metas" style="top: 180px; left: 400px;">
            <div>Arquitectura de Software</div>
        </div>

        <div class="branch-line" style="top: 300px; left: 400px; width: 75px; transform: rotate(-75deg);"></div>
        <div class="category metas" style="top: 170px; left: 430px;">
            <div>Liderazgo Técnico</div>
        </div>
    </div>
</body>
</html>
