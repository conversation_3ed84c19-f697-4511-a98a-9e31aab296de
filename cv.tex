\documentclass[11pt,a4paper]{moderncv}

\moderncvstyle{classic}
\moderncvcolor{blue}

\usepackage[scale=0.85]{geometry}
\usepackage[utf8]{inputenc}
\usepackage{lmodern}
\usepackage{hyperref}
\usepackage{fontawesome5}

\name{<PERSON>}{<PERSON><PERSON>}
\title{Full Stack Developer}
\address{Bogota, D.C., Capital District}{Colombia}
\phone{+57 3053908550}
\email{<EMAIL>}
\social[linkedin]{nicolas-pardo-6156a1222}
\social[github]{Nick220505}

\begin{document}
\makecvtitle

\section{Summary}
Systems Engineering student (6th semester) with a strong passion for web development. Experienced in designing and implementing modern web applications using best practices. Proficient in full-stack development with expertise in Next.js, React, Angular, Spring Boot, FastAPI, Django, Node.js, and database technologies including PostgreSQL, Oracle DB (PL/SQL), MongoDB and MySQL. Skilled in designing type-safe, scalable systems and modern frontend interfaces. Familiar with layered architecture, hexagonal architecture, feature sliced architecture (FSA), and SOLID principles. Experienced in implementing role-based access control, multi-tenant systems, and secure authentication workflows. Fluent in English with advanced proficiency, enabling effective collaboration in international environments.\newline
Additionally, I am a self-taught developer, and most of my knowledge has been acquired independently.

\section{Professional Experience}
\cventry{Feb 2025--Present}{Full Stack Developer - Insolventic Project}{Armonia Concertada - Constructores de Paz}{Bogota, Colombia}{}{\begin{itemize}
\item Frontend: Next.js 15, React 19, Tailwind CSS, Shadcn UI components, TypeScript, React Hook Form, Optimistic UI
\item Backend: Next.js Server Actions, Prisma ORM, PostgreSQL, Zod validation schemas
\item Full Stack: Feature Sliced Architecture (FSA), type-safe end-to-end design, JWT authentication
\item DevOps: GitHub Actions, Vercel deployment, automated CI/CD pipelines, environment separation
\item Developing a comprehensive insolvency process management system for legal professionals in compliance with Colombian law. Implementing multi-tenant architecture with role-based access control and fine-grained permissions. Building complete case management workflow including debtor profiles, creditor management, hearing scheduling, and document generation. Creating interactive dashboards with real-time analytics and ensuring secure data handling following legal requirements.
\end{itemize}}

\cventry{Jun 2023--Dec 2024}{Junior Full Stack Developer}{Juana Archila S.A.S}{Bogota, Colombia}{}{\begin{itemize}
\item Frontend: HTML5, CSS3, TailwindCSS, React, Angular.
\item Backend: Python (Django, FastAPI), JavaScript (Express.js).
\item Full Stack: Next.js, PostgreSQL, MongoDB.
\item DevOps: Docker, GitHub Actions.
\item Juan demonstrated exceptional commitment to learning and continuous improvement, adapting to new technologies and focusing on software quality. He possesses the necessary skills to perform successfully in the role of Full Stack Developer.
\end{itemize}}

\section{Education}
\cventry{2023--2027}{Systems Engineering (6th semester)}{Universidad El Bosque}{Bogota, Colombia}{}{}
\cventry{May 2024--Nov 2024}{Full Stack Development - Full Stack Open 2024}{University of Helsinki}{Online}{}{
\begin{itemize}
\item Comprehensive curriculum covering modern web development across frontend, backend, DevOps, and mobile
\item Frontend: React, Redux, React Query, TypeScript, Material-UI, styled-components, React Native
\item Backend: Node.js, Express, REST APIs, GraphQL, Apollo, MongoDB, PostgreSQL, Sequelize ORM
\item DevOps \& Tools: Docker, CI/CD with GitHub Actions, Jest, Playwright, Webpack, ESLint
\item Advanced concepts: State management, authentication, webhooks, custom hooks, TypeScript integration
\item Course repository: \href{https://gitfront.io/r/Nick220505/j6RXXGqXsxRU/fullstackopen/}{\faGithub\ \texttt{gitfront.io/r/Nick220505/j6RXXGqXsxRU/fullstackopen}}
\end{itemize}}
\cventry{Jun 2024--Jul 2024}{CS50W: Web Programming with Python and JavaScript}{Harvard University}{Online}{}{
\begin{itemize}
\item Advanced web development focusing on full-stack applications
\item Topics: Web Security, Scalability, User Experience Design, Cloud Services
\item Technologies: Django, React, Git, Heroku, Database Design and Migrations
\end{itemize}}
\cventry{Jun 2024--Jul 2024}{CS50SQL: Introduction to Databases with SQL}{Harvard University}{Online}{}{
\begin{itemize}
\item Comprehensive study of relational databases and SQL mastery
\item Topics: Data Modeling, Table Normalization, Views, Indexes, Database Integration
\item Technologies: SQLite, PostgreSQL, MySQL, Database Design and Optimization
\end{itemize}}
\cventry{Jun 2023--Jul 2023}{CS50P: Introduction to Programming with Python}{Harvard University}{Online}{}{
\begin{itemize}
\item Advanced Python programming concepts and object-oriented programming
\item Topics: Functions, Variables, Conditionals, Loops, Regular Expressions
\item Focus on unit testing, file I/O, libraries, and exception handling
\end{itemize}}
\cventry{Apr 2023--Aug 2023}{CS50x: Introduction to Computer Science}{Harvard University}{Online}{}{
\begin{itemize}
\item Comprehensive study of computer science fundamentals, algorithms, and data structures
\item Topics: Algorithmic Thinking, Memory Management, Software Engineering Principles, Linux Command Line
\item Technologies: C, Python, SQL, HTML, CSS, JavaScript, Flask
\end{itemize}}
\cventry{Apr 2023--May 2023}{CS50T: Computer Science for Technology}{Harvard University}{Online}{}{
\begin{itemize}
\item Core technology concepts: Internet, Multimedia, Security, Web Development
\item Focus on understanding technology fundamentals and principles
\item Web technologies: HTML, CSS, Bootstrap
\end{itemize}}

\section{Technical Projects}
\subsection{Contract Management System}
\cventry{May 2024--Present}{Full Stack Developer}{}{}{}{
\begin{itemize}
\item Developed a comprehensive contract management system with user role-based access control
\item Backend: Python, FastAPI, PostgreSQL, Keycloak authentication, email integration
\item Frontend: Angular 17, TypeScript, Angular Material, RxJS
\item DevOps: Docker, Docker Compose, Nginx
\item Features: Document management, contract lifecycle tracking, audit logging, reporting, dynamic forms
\item Implemented security features including HTTPS enforcement, protection against common vulnerabilities
\item Created comprehensive documentation and detailed monthly progress reports
\item Handled end-to-end development including UI/UX design, backend architecture, and deployment
\item Reference contact: Miguel Ángel Porras Villarreal, +57 3106197516
\end{itemize}}

\subsection{Breathe Coherence - Holistic E-commerce Platform}
\cventry{Jan 2025--Present}{Full Stack Developer}{}{}{}{
\begin{itemize}
\item Developed a modern e-commerce platform for sacred geometry and healing essences
\item Frontend: Next.js 15, React 19, TailwindCSS, Server Components
\item Backend: Prisma ORM, Database migrations, RESTful APIs
\item Features: AI-powered chatbot using Gemini API, payment processing, inventory management
\item Implemented responsive design, SEO optimization, and performance monitoring
\item Project repository: \href{https://gitfront.io/r/Nick220505/KNgz9APuLsyo/breathecoherence/}{\faGithub\ \texttt{gitfront.io/r/Nick220505/KNgz9APuLsyo/breathecoherence}}
\item Live website: \href{https://www.breathecoherence.com/}{\faGlobe\ \texttt{www.breathecoherence.com}}
\end{itemize}}

\subsection{Entrecol - Entertainment \& Employee Management System}
\cventry{Oct 2024--Nov 2024}{Full Stack Developer}{}{}{}{
\begin{itemize}
\item Built a comprehensive web application combining entertainment content analytics with employee management
\item Frontend: Angular 18, TypeScript, Angular Material, NgxCharts, Signal-based state management
\item Backend: Spring Boot 3, Java 21, JPA/Hibernate, MySQL, JWT authentication
\item DevOps: Docker, Docker Compose, Multi-stage builds, Health monitoring
\item Implemented real-time data visualization, automated report generation, and secure authentication
\end{itemize}}

\subsection{Bloglist - Blog Management Application}
\cventry{Jun 2024--Jul 2024}{Full Stack Developer}{}{}{}{
\begin{itemize}
\item Developed a full-stack blog management system with 97\%+ test coverage
\item Frontend: React 18, Material UI, React Query, React Router, Vite
\item Backend: Node.js, Express, MongoDB, JWT authentication
\item Testing: Playwright (E2E), Vitest, Jest, React Testing Library
\item DevOps: Docker, Nginx, GitHub Actions CI/CD, Render.com deployment
\end{itemize}}

\section{Technical Skills}
\cvitem{Languages}{Java, TypeScript, JavaScript, Python, C, C\#, SQL, YAML}
\cvitem{Frontend}{Next.js, React, Angular, React Native, HTML5, CSS3, SASS, Material UI, Bootstrap, TailwindCSS, Shadcn UI, Angular Material, PrimeNG, JSF, PrimeFaces, RxJS, Redux}
\cvitem{Backend}{Spring Boot, Spring Security, Node.js, Express, Django, FastAPI, Flask, Prisma ORM, Java JSP, Hibernate, Jakarta Persistence, Sequelize.js, GraphQL}
\cvitem{Databases}{PostgreSQL, MySQL, Oracle DB, PL/SQL (Stored Procedures, Functions, Triggers, Packages, Cursors), MongoDB, CockroachDB, Supabase, SQLite, Database Migrations, Database Design, Table Normalization, Views, Materialized Views, Indexes, Partitioning, Query Optimization, Performance Tuning, Transaction Management, Concurrency Control, SQL Injection Prevention, Database Security}
\cvitem{AI/Integration}{Gemini API, Chatbot Development, Third-party API Integration, reCAPTCHA}
\cvitem{Testing}{Playwright, Jest, Vitest, JUnit, Test-Driven Development, Unit Testing, Integration Testing, End-to-end Testing}
\cvitem{DevOps}{Docker, Docker Orchestration, GitHub Actions, CI/CD, Nginx, Heroku, Vercel, Koyeb, Git Version Control}
\cvitem{Tools}{Git, VS Code, Eclipse IDE, PHPMyAdmin, pgAdmin, Angular CLI, JFreeChart, Project Lombok}
\cvitem{Operating Systems}{Linux, Command Line Interface (CLI) Proficiency}
\cvitem{Core Concepts}{REST APIs, AJAX, Data Structures, Algorithms, Object-Oriented Programming, Memory Management, Software Engineering Principles, Web Security, Scalability, User Experience Design}

\section{Certifications}
\cventry{Jan 2024}{Foundational C\# with Microsoft}{freeCodeCamp}{}{}{}
\cventry{Jun 2024}{CI/CD}{University of Helsinki}{}{}{}
\cventry{2024}{CS50W Certificate}{Harvard University}{}{}{}
\cventry{2024}{CS50SQL Certificate}{Harvard University}{}{}{}
\cventry{2023}{CS50x Certificate}{Harvard University}{}{}{}
\cventry{2023}{CS50P Certificate}{Harvard University}{}{}{}
\cventry{2023}{CS50T Certificate}{Harvard University}{}{}{}

\section{Languages}
\cvitem{English}{Advanced proficiency}
\cvitem{Spanish}{Native proficiency}

\end{document}